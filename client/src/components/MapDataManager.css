/* Map Data Manager Styles */
.map-data-manager {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--spacing-sm);
  padding: var(--spacing-md);
  max-width: 400px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border-light);
}

.manager-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.map-stats {
  display: flex;
  gap: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.map-stats span {
  background: var(--color-bg-tertiary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--spacing-xs);
  border: 1px solid var(--color-border);
}

/* Manager Sections */
.manager-section {
  margin-bottom: var(--spacing-lg);
}

.manager-section:last-child {
  margin-bottom: 0;
}

.manager-section h5 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Button Groups */
.button-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.manager-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--spacing-xs);
  color: var(--color-text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
}

.manager-button:hover:not(:disabled) {
  background: var(--color-bg-tertiary);
  border-color: var(--color-accent);
  transform: translateY(-1px);
}

.manager-button:active:not(:disabled) {
  transform: translateY(0);
}

.manager-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.manager-button.primary {
  background: var(--color-accent);
  color: white;
  border-color: var(--color-accent);
}

.manager-button.primary:hover:not(:disabled) {
  background: var(--color-accent-dark);
  border-color: var(--color-accent-dark);
}

.manager-button.warning {
  background: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.manager-button.warning:hover:not(:disabled) {
  background: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
}

/* Backup Info */
.backup-info {
  margin-top: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  font-style: italic;
  line-height: 1.4;
}

/* Map Information */
.map-info {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--spacing-xs);
  padding: var(--spacing-sm);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--color-border-light);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.info-value {
  font-size: 0.75rem;
  color: var(--color-text-primary);
  font-weight: 600;
}

/* Error Display */
.manager-error {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-error);
  color: white;
  padding: var(--spacing-sm);
  border-radius: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
  font-size: 0.875rem;
}

.error-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.error-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* TODO Notice */
.todo-notice {
  background: var(--color-bg-tertiary);
  border: 1px solid var(--color-border);
  border-radius: var(--spacing-xs);
  padding: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.todo-notice h6 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.todo-notice ul {
  margin: 0;
  padding-left: var(--spacing-md);
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.todo-notice li {
  margin-bottom: var(--spacing-xs);
}

.todo-notice li:last-child {
  margin-bottom: 0;
}

/* Loading States */
.manager-button:disabled {
  position: relative;
}

.manager-button:disabled::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .map-data-manager {
    max-width: none;
    margin: 0 var(--spacing-sm);
  }

  .manager-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .map-stats {
    flex-wrap: wrap;
    width: 100%;
  }

  .button-group {
    gap: var(--spacing-xs);
  }

  .manager-button {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    min-height: 36px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .todo-notice {
    font-size: 0.7rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .manager-button {
    border-width: 2px;
  }

  .map-stats span {
    border-width: 2px;
  }

  .map-info {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .manager-button {
    transition: none;
  }

  .manager-button:hover:not(:disabled) {
    transform: none;
  }

  .manager-button:active:not(:disabled) {
    transform: none;
  }

  @keyframes spin {
    to {
      transform: none;
    }
  }
}
