/* Fabric.js Map Canvas Styles */
.fabric-map-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--spacing-xs);
}

.fabric-map-canvas canvas {
  display: block;
  cursor: crosshair;
  background: transparent;
}

/* Canvas error display */
.canvas-error {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: var(--color-error);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--spacing-xs);
  font-size: 0.875rem;
  z-index: 1000;
  max-width: 300px;
  word-wrap: break-word;
}

/* Fabric.js control customizations */
.fabric-map-canvas .canvas-container {
  position: relative;
}

/* Custom cursor states */
.fabric-map-canvas.mode-select canvas {
  cursor: default;
}

.fabric-map-canvas.mode-move canvas {
  cursor: move;
}

.fabric-map-canvas.mode-draw canvas {
  cursor: crosshair;
}

.fabric-map-canvas.mode-erase canvas {
  cursor: not-allowed;
}

/* Object selection styles */
.fabric-map-canvas .canvas-container .upper-canvas {
  outline: none;
}

/* Grid overlay styles */
.fabric-map-canvas .grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Interactive area styles */
.fabric-map-canvas .interactive-area {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.fabric-map-canvas .interactive-area:hover {
  opacity: 1;
}

.fabric-map-canvas .interactive-area.selected {
  opacity: 1;
  filter: brightness(1.1);
}

/* Collision area styles */
.fabric-map-canvas .collision-area {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.fabric-map-canvas .collision-area:hover {
  opacity: 0.8;
}

.fabric-map-canvas .collision-area.selected {
  opacity: 0.9;
}

/* Layer visibility controls */
.fabric-map-canvas .layer-controls {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--spacing-xs);
  padding: var(--spacing-xs);
  z-index: 100;
}

.fabric-map-canvas .layer-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  font-size: 0.75rem;
}

.fabric-map-canvas .layer-control:last-child {
  margin-bottom: 0;
}

.fabric-map-canvas .layer-control input[type="checkbox"] {
  margin: 0;
}

.fabric-map-canvas .layer-control label {
  margin: 0;
  cursor: pointer;
  user-select: none;
}

/* Canvas loading state */
.fabric-map-canvas.loading {
  opacity: 0.7;
  pointer-events: none;
}

.fabric-map-canvas.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid var(--color-border-light);
  border-top-color: var(--color-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1000;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .fabric-map-canvas {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .fabric-map-canvas .layer-controls {
    position: static;
    margin-bottom: var(--spacing-sm);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .fabric-map-canvas .layer-control {
    margin-bottom: 0;
    flex: 1;
    min-width: 120px;
  }

  .canvas-error {
    position: static;
    margin-bottom: var(--spacing-sm);
    max-width: none;
  }
}

/* High DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .fabric-map-canvas canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Focus and accessibility */
.fabric-map-canvas:focus-within {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

.fabric-map-canvas canvas:focus {
  outline: none;
}

/* Selection indicators */
.fabric-map-canvas .selection-info {
  position: absolute;
  bottom: var(--spacing-sm);
  left: var(--spacing-sm);
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  z-index: 100;
}

/* Zoom controls */
.fabric-map-canvas .zoom-controls {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  z-index: 100;
}

.fabric-map-canvas .zoom-button {
  width: 32px;
  height: 32px;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-light);
  border-radius: var(--spacing-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  color: var(--color-text-primary);
  transition: all 0.2s ease;
}

.fabric-map-canvas .zoom-button:hover {
  background: var(--color-bg-tertiary);
  border-color: var(--color-accent);
}

.fabric-map-canvas .zoom-button:active {
  transform: scale(0.95);
}

/* Performance optimizations */
.fabric-map-canvas canvas {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Print styles */
@media print {
  .fabric-map-canvas .layer-controls,
  .fabric-map-canvas .zoom-controls,
  .fabric-map-canvas .selection-info,
  .canvas-error {
    display: none;
  }

  .fabric-map-canvas {
    border: none;
    background: white;
  }
}
