.video-call-module {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.video-call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.video-call-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.call-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.call-status {
  font-weight: 500;
}

.error-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8d7da;
  color: #721c24;
  border-bottom: 1px solid #f5c6cb;
}

.error-message button {
  background: none;
  border: none;
  color: #721c24;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
}

.video-container {
  flex: 1;
  position: relative;
  background: #000;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.join-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.join-content {
  text-align: center;
  padding: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.join-content h4 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
}

.join-content p {
  margin: 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.join-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.2s;
}

.join-button:hover {
  background: #0056b3;
}

.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f8f9fa;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.jitsi-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.jitsi-container.hidden {
  display: none;
}

.jitsi-container.active {
  display: block;
}

.participants-list {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.participants-list h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.participants {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.participant {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .video-call-header {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .call-info {
    justify-content: center;
  }

  .join-content {
    padding: 24px 16px;
  }

  .join-content h4 {
    font-size: 20px;
  }

  .participants {
    justify-content: center;
  }
}
