.world-module {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-bg-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border-radius: 0;
}

.world-container {
  flex: 1;
  display: flex;
}

.game-canvas {
  flex: 1;
  background: #87CEEB;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  position: relative;
}

.game-canvas canvas {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
}



/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.loading-modal {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--spacing-md);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  color: var(--color-text-primary);
}

.loading-spinner {
  margin-bottom: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.meeting-modal {
  background: white;
  border-radius: 20px;
  padding: 0;
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  color: #333;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.1);
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  z-index: 10;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.2);
}

.modal-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.modal-header h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.modal-content {
  padding: 30px;
}

.participants-section {
  margin-bottom: 30px;
}

.participants-section h4 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.online-count {
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 16px;
  display: block;
}

.participants-list {
  display: flex;
  gap: 8px;
  align-items: center;
}

.participant-avatar {
  position: relative;
}

.avatar-circle-small {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  color: white;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.participant-avatar.add-more .avatar-circle-small {
  background: #f0f0f0;
  color: #999;
  border: 2px dashed #ddd;
}

.modal-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.join-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 20px;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.join-button.primary {
  background: #333;
  color: white;
}

.join-button.primary:hover {
  background: #555;
  transform: translateY(-1px);
}

.join-button.secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
}

.join-button.secondary:hover {
  background: #e9ecef;
}

.join-button.tertiary {
  background: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.join-button.tertiary:hover {
  background: rgba(102, 126, 234, 0.1);
}

.button-icon {
  font-size: 1.1rem;
}

@media (max-width: 768px) {

  .meeting-modal {
    width: 95%;
    max-width: none;
  }

  .modal-header {
    padding: 30px 20px 20px;
  }

  .modal-content {
    padding: 20px;
  }
}
