WorldModule.tsx:604 Creating Phaser game...
WorldModule.tsx:622      Phaser v3.90.0 (WebGL | Web Audio)  https://phaser.io/v390
WorldModule.tsx:623 Phaser game created successfully: Game {config: Config, renderer: WebG<PERSON>enderer, domContainer: null, canvas: canvas, context: WebGLRenderingContext, …}
WorldModule.tsx:632 Cleaning up Phaser game...
WorldModule.tsx:604 Creating Phaser game...
WorldModule.tsx:622      Phaser v3.90.0 (WebGL | Web Audio)  https://phaser.io/v390
WorldModule.tsx:623 Phaser game created successfully: Game {config: Config, renderer: WebGLRenderer, domContainer: null, canvas: canvas, context: WebGLRenderingContext, …}
PhaserMapRenderer.ts:74 PhaserMapRenderer groups initialized successfully
WorldModule.tsx:148 Initializing player avatar for: admin
WorldModule.tsx:152 Loading player avatar sprite sheet...
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.846Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Sprite sheet texture loading begins
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.846Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Loading avatar configuration
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.846Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Avatar config loaded successfully {configKeys: Array(5), configValid: true}
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.846Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Composing avatar sprite sheet from config
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.878Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Avatar sprite sheet composed successfully {urlLength: 117886, dataType: 'base64', compositionSuccess: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.878Z] [AvatarRenderer:SpriteSheetLoading] [admin] Creating sprite sheet using Phaser built-in system {textureKey: 'avatar_sheet_admin'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.884Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Texture dimensions loaded {width: 384, height: 384, naturalWidth: 384, naturalHeight: 384}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.884Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Texture validation status {validDimensions: true, expectedSize: true, isSquare: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.885Z] [AvatarRenderer:SpriteSheetLoading] [admin] Calculated frame dimensions for 3x3 grid {frameWidth: 128, frameHeight: 128, totalFrames: 9}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.885Z] [AvatarRenderer:PhaserTextureProcessing] [admin] Calling addSpriteSheet() with frame params {frameWidth: 128, frameHeight: 128, startFrame: 0, endFrame: 8}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.892Z] [AvatarRenderer:PhaserTextureProcessing] [admin] Sprite sheet registered in Phaser texture manager {textureKey: 'avatar_sheet_admin', frameCount: 9, registrationSuccess: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.892Z] [AvatarRenderer:PhaserTextureProcessing] [admin] Frame validation completed {totalFrames: 9, validFrames: 9, allFramesValid: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.893Z] [AvatarRenderer:AnimationSystemSetup] [admin] Starting animation system setup {spriteSheetKey: 'avatar_sheet_admin', totalAnimations: 5}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.893Z] [AvatarRenderer:AnimationCreation] [admin] Creating new animations (some may not exist yet) {existingAnims: Array(5)}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.893Z] [AvatarRenderer:AnimationSystemSetup] [admin] Creating walk_down animation {key: 'admin_walk_down', frames: Array(3), frameRate: 8}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.893Z] [AvatarRenderer:AnimationSystemSetup] [admin] walk_down registration result {success: true, key: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.894Z] [AvatarRenderer:AnimationCreation] [admin] Creating walk_left animation with sprite sheet frames {animationKey: 'admin_walk_left', frames: Array(3), spriteSheetKey: 'avatar_sheet_admin'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.894Z] [AvatarRenderer:AnimationCreation] [admin] Creating walk_up animation with sprite sheet frames {animationKey: 'admin_walk_up', frames: Array(3), spriteSheetKey: 'avatar_sheet_admin'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.894Z] [AvatarRenderer:AnimationCreation] [admin] Creating walk_right animation with sprite sheet frames {animationKey: 'admin_walk_right', frames: Array(3), spriteSheetKey: 'avatar_sheet_admin', note: 'Reusing left frames, sprite will be flipped'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.894Z] [AvatarRenderer:AnimationCreation] [admin] Creating idle animation with sprite sheet frame {animationKey: 'admin_idle', frame: 1, spriteSheetKey: 'avatar_sheet_admin'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.895Z] [AvatarRenderer:AnimationCreation] [admin] Sprite sheet animation creation completed {totalAnimations: 5, successfulAnimations: 5, createdKeys: Array(5), failedKeys: Array(0), method: 'Phaser built-in sprite sheet system'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.895Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Sprite sheet texture loading completes {textureKey: 'avatar_sheet_admin', loadingSuccess: true, readyForSprites: true}
WorldModule.tsx:164 Creating animated avatar sprite...
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.895Z] [AvatarRenderer:SpriteDisplay] [admin] Creating animated player sprite {x: 400, y: 300}
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.895Z] [AvatarRenderer:SpriteDisplay] [admin] No existing sprite found, creating new sprite
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.895Z] [AvatarRenderer:SpriteObjectCreation] [admin] Starting Phaser sprite object instantiation {position: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.896Z] [AvatarRenderer:SpriteObjectCreation] [admin] Selected texture for sprite creation {textureKey: 'terra-branford', textureExists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.896Z] [AvatarRenderer:SpriteObjectCreation] [admin] Phaser sprite object instantiated {spriteId: 'unnamed', textureKey: 'terra-branford'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.896Z] [AvatarRenderer:SpriteObjectCreation] [admin] Initial texture assignment completed {displaySize: {…}, position: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.896Z] [AvatarRenderer:SceneIntegration] [admin] Sprite added to scene and registered {spriteCount: 1, visible: true, active: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.897Z] [AvatarRenderer:SpriteDisplay] [admin] Default sprite created successfully {textureKey: 'terra-branford', position: {…}}
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.897Z] [AvatarRenderer:SpriteDisplay] [admin] Starting asynchronous avatar sprite sheet loading
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.897Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Sprite sheet texture loading begins
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.897Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Loading avatar configuration
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.897Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Avatar config loaded successfully {configKeys: Array(5), configValid: true}
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.897Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Composing avatar sprite sheet from config
WorldModule.tsx:177 Player animated avatar updated successfully
WorldModule.tsx:459 Starting idle animation for admin
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.902Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'idle', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.903Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'terra-branford', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.903Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.903Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_idle', lastAnimKey: undefined, currentAnimKey: null, isPlaying: false, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.903Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_idle', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.904Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.904Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_idle', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.904Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_idle', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.918Z] [AvatarRenderer:SpriteLoadingPhase] [admin] Avatar sprite sheet composed successfully {urlLength: 117886, dataType: 'base64', compositionSuccess: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.918Z] [AvatarRenderer:SpriteSheetLoading] [admin] Creating sprite sheet using Phaser built-in system {textureKey: 'avatar_sheet_admin'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.919Z] [AvatarRenderer:SpriteSheetLoading] [admin] Texture already exists, reusing existing sprite sheet {textureKey: 'avatar_sheet_admin'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.919Z] [AvatarRenderer:AnimationSystemSetup] [admin] Starting animation system setup {spriteSheetKey: 'avatar_sheet_admin', totalAnimations: 5}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.919Z] [AvatarRenderer:AnimationCreation] [admin] All animations already exist, skipping creation {existingAnimKeys: Array(5)}
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.919Z] [AvatarRenderer:SpriteSheetLoading] [admin] Avatar sprite sheet loading completed successfully (reused existing)
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.919Z] [AvatarRenderer:SpriteDisplay] [admin] Avatar sprite sheet loading completed {textureKey: 'avatar_sheet_admin', textureExists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.919Z] [AvatarRenderer:SpriteDisplay] [admin] Updating existing sprite with sprite sheet texture {oldTexture: 'avatar_sheet_admin', newSpriteSheetTexture: 'avatar_sheet_admin', method: 'Phaser built-in sprite sheet'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.920Z] [AvatarRenderer:SpriteDisplay] [admin] Sprite resized to match sprite sheet frame dimensions {totalSize: {…}, frameSize: {…}, spriteSheetKey: 'avatar_sheet_admin', method: 'Phaser sprite sheet frame sizing (3x3 grid)'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.920Z] [AvatarRenderer:SpriteDisplay] [admin] Attempting to play idle animation from sprite sheet {animationKey: 'admin_idle', animationExists: true, spriteSheetKey: 'avatar_sheet_admin'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.920Z] [AvatarRenderer:SceneIntegration] [admin] Initial animation playback setup completed {animationKey: 'admin_idle', isPlaying: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.920Z] [AvatarRenderer:SceneIntegration] [admin] Sprite visible and active in scene {visible: true, active: true, depth: 10}
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.921Z] [AvatarRenderer:MovementReadiness] [admin] Starting sprite readiness validation
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.921Z] [AvatarRenderer:MovementReadiness] [admin] Sprite state validation {exists: true, active: true, visible: true, ready: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.921Z] [AvatarRenderer:MovementReadiness] [admin] Texture state validation {hasTexture: true, textureKey: 'avatar_sheet_admin', valid: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.922Z] [AvatarRenderer:MovementReadiness] [admin] Animation availability check {allAnimationsExist: true, totalAnimations: 5}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.922Z] [AvatarRenderer:MovementReadiness] [admin] Scene integration validation {registeredInMap: true, spriteMapSize: 1}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:33.922Z] [AvatarRenderer:MovementReadiness] [admin] Final readiness status {ready: true, canAcceptMovement: true, enableUserControls: true}
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.922Z] [AvatarRenderer:MovementReadiness] [admin] Sprite fully initialized and ready for movement commands
AvatarGameRenderer.ts:36 [2025-09-05T00:16:33.922Z] [AvatarRenderer:SpriteDisplay] [admin] Avatar sprite updated successfully with sprite sheet system
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.711Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.712Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.712Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.712Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_idle', currentAnimKey: 'admin_idle', isPlaying: false, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.712Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.712Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.712Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.713Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.724Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.725Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.725Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.725Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.725Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.725Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.726Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.726Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.738Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.738Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.738Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.739Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.739Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.739Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.739Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.739Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.751Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.751Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.751Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.752Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.752Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.752Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.752Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.753Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.764Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.765Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.765Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.765Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.765Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.765Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.765Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.766Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.778Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.778Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.778Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.778Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.779Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.779Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.779Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.779Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
 [2025-09-05T00:16:37.791Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
 [2025-09-05T00:16:37.791Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
 [2025-09-05T00:16:37.791Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
 [2025-09-05T00:16:37.792Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
 [2025-09-05T00:16:37.792Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
 [2025-09-05T00:16:37.792Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
 [2025-09-05T00:16:37.792Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
 [2025-09-05T00:16:37.792Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
 [2025-09-05T00:16:37.804Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
 [2025-09-05T00:16:37.804Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
 [2025-09-05T00:16:37.805Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.805Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.805Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.805Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.805Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.806Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.818Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.818Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.818Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.818Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.818Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.819Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.819Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.819Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.831Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.831Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.831Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.832Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.832Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.832Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.832Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.832Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.844Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.845Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.845Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.845Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.845Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.845Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.845Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.846Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.858Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.858Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.858Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.858Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.858Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.858Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.859Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.859Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.871Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.871Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.871Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.871Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.871Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.872Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.872Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.872Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.884Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.885Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.885Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.885Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.885Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.885Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.885Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.886Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.898Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.898Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.898Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.898Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.898Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.899Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.899Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.899Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.911Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.911Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.912Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.912Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.912Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.912Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.913Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.913Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.924Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.924Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.925Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.925Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.925Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.925Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.925Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.926Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.937Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.938Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.938Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.938Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.939Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.939Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.939Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.939Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.951Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.951Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.951Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.952Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.952Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.952Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.952Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.952Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.964Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.964Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.964Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.965Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.965Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.965Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.965Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.965Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.978Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.978Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.978Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.978Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.978Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.978Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.979Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.979Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.991Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.991Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.992Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.992Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.992Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.992Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.992Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:37.993Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.004Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.004Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.004Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.005Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.005Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.005Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.005Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.005Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.018Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'down', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.018Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.018Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.018Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_down', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.019Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_down', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.019Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_down'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.019Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_down', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.019Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_down', flipX: false, finalState: {…}}
WorldModule.tsx:459 Starting idle animation for admin
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.031Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'idle', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.031Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.031Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.031Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_idle', lastAnimKey: 'admin_walk_down', currentAnimKey: 'admin_walk_down', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.032Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_idle', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.032Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.032Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_idle', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.032Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_idle', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.231Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.231Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.231Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.232Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_idle', currentAnimKey: 'admin_idle', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.232Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.232Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.232Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.232Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.244Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.244Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.245Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.245Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.245Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.245Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.245Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.245Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.258Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.258Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.258Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.258Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.258Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.259Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.259Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.259Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.271Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.271Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.271Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.272Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.272Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.272Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.272Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.272Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.284Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.285Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.285Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.285Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.285Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.285Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.286Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.286Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.298Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.298Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.298Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.298Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.298Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.298Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.299Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.299Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.312Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.324Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.324Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.325Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.325Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.325Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.325Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.325Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.325Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.338Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.338Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.338Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.338Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.338Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.338Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.338Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.339Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.351Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: true, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.351Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.351Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.351Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.351Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.352Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.352Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.352Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: true, finalState: {…}}
WorldModule.tsx:459 Starting idle animation for admin
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.364Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'idle', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.365Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.365Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.365Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_idle', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.365Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_idle', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.365Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.365Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_idle', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.365Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_idle', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.724Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.724Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.724Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.725Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_idle', currentAnimKey: 'admin_idle', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.725Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.725Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.725Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.725Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.738Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.738Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.738Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.738Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.738Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.738Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.739Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.739Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.751Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.751Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.751Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.752Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.752Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.752Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.752Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.752Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.764Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.765Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.765Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.765Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.765Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.765Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.765Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.765Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.778Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.778Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.778Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.779Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.779Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.779Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.779Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.779Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.791Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.791Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.791Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.791Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.792Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.792Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.792Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.792Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: false, finalState: {…}}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.804Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'left', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.804Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.805Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.805Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_walk_left', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.805Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_walk_left', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.805Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_walk_left'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.805Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_walk_left', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.805Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_walk_left', flipX: false, finalState: {…}}
WorldModule.tsx:459 Starting idle animation for admin
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.818Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback requested {direction: 'idle', flipX: false, hasFlipXParameter: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.818Z] [AvatarRenderer:AnimationPlayback] [admin] Sprite found for animation {currentTexture: 'avatar_sheet_admin', spritePosition: {…}, spriteVisible: true, spriteActive: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.818Z] [AvatarRenderer:AnimationPlayback] [admin] Animation key determined {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.818Z] [AvatarRenderer:AnimationPlayback] [admin] Checking animation state {requestedAnimKey: 'admin_idle', lastAnimKey: 'admin_walk_left', currentAnimKey: 'admin_walk_left', isPlaying: true, flipXRequested: false}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.818Z] [AvatarRenderer:AnimationPlayback] [admin] Animation existence check {animKey: 'admin_idle', exists: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.819Z] [AvatarRenderer:AnimationPlayback] [admin] Starting animation playback {animKey: 'admin_idle'}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.819Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback initiated {newCurrentAnimKey: 'admin_idle', isNowPlaying: true, animationStarted: true}
AvatarGameRenderer.ts:34 [2025-09-05T00:16:38.819Z] [AvatarRenderer:AnimationPlayback] [admin] Animation playback completed successfully {animKey: 'admin_idle', flipX: false, finalState: {…}}
